// This file is generated by (vscode-gitlens)/scripts/export-codicons.js
// Do not edit this file directly

$icon-font-family: 'glicons';

$icon-map: (
	'commit-horizontal': '\f101',
	'graph': '\f102',
	'next-commit': '\f103',
	'prev-commit-menu': '\f104',
	'prev-commit': '\f105',
	'compare-ref-working': '\f106',
	'branches-view': '\f107',
	'commit-view': '\f108',
	'commits-view': '\f109',
	'compare-view': '\f10a',
	'contributors-view': '\f10b',
	'history-view': '\f10c',
	'history': '\f10c',
	'remotes-view': '\f10d',
	'repositories-view': '\f10e',
	'search-view': '\f10f',
	'stashes-view': '\f110',
	'stashes': '\f110',
	'tags-view': '\f111',
	'worktrees-view': '\f112',
	'gitlens': '\f113',
	'stash-pop': '\f114',
	'stash-save': '\f115',
	'unplug': '\f116',
	'open-revision': '\f117',
	'switch': '\f118',
	'expand': '\f119',
	'list-auto': '\f11a',
	'pinned-filled': '\f11c',
	'clock': '\f11d',
	'provider-azdo': '\f11e',
	'provider-bitbucket': '\f11f',
	'provider-gerrit': '\f120',
	'provider-gitea': '\f121',
	'provider-github': '\f122',
	'provider-gitlab': '\f123',
	'gitlens-inspect': '\f124',
	'workspaces-view': '\f125',
	'confirm-checked': '\f126',
	'confirm-unchecked': '\f127',
	'cloud-patch': '\f128',
	'cloud-patch-share': '\f129',
	'inspect': '\f12a',
	'repository-filled': '\f12b',
	'gitlens-filled': '\f12c',
	'code-suggestion': '\f12d',
	'provider-jira': '\f133',
	'play-button': '\f134',
	'rocket-filled': '\f135',
	'branches-view-filled': '\f136',
	'commits-view-filled': '\f137',
	'contributors-view-filled': '\f138',
	'remotes-view-filled': '\f139',
	'repositories-view-filled': '\f13a',
	'search-view-filled': '\f13b',
	'stashes-view-filled': '\f13c',
	'tags-view-filled': '\f13d',
	'worktrees-view-filled': '\f13e',
	'launchpad-view': '\f13f',
	'launchpad-view-filled': '\f140',
	'merge-target': '\f141',
	'history-view-filled': '\f142',
);
