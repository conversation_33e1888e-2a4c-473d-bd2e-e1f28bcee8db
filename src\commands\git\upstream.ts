import { window } from 'vscode';
import type { Container } from '../../container';
import { BranchError } from '../../git/errors';
import type { GitBranchReference } from '../../git/models/reference';
import type { Repository } from '../../git/models/repository';
import { isRemoteBranch } from '../../git/utils/branch.utils';
import { showGenericErrorMessage } from '../../messages';
import type { QuickPickItemOfT } from '../../quickpicks/items/common';
import { Logger } from '../../system/logger';
import type {
	AsyncStepResultGenerator,
	PartialStepState,
	StepGenerator,
	StepResultGenerator,
	StepSelection,
} from '../quickCommand';
import {
	canPickStepContinue,
	createConfirmStep,
	createPickStep,
	endSteps,
	QuickCommand,
	StepResultBreak,
} from '../quickCommand';
import { appendReposToTitle, pickBranchStep, pickRepositoryStep } from '../quickCommand.steps';

interface Context {
	repos: Repository[];
	associatedView: any;
	title: string;
}

interface SetState {
	subcommand: 'set';
	repo: string | Repository;
	branch: GitBranchReference;
	upstream: GitBranchReference;
}

interface UnsetState {
	subcommand: 'unset';
	repo: string | Repository;
	branch: GitBranchReference;
}

interface ChangeState {
	subcommand: 'change';
	repo: string | Repository;
	branch: GitBranchReference;
	upstream: GitBranchReference;
}

type State = SetState | UnsetState | ChangeState;

function isSetState(state: Partial<State> | undefined): state is Partial<SetState> {
	return state?.subcommand === 'set';
}

function isChangeState(state: Partial<State> | undefined): state is Partial<ChangeState> {
	return state?.subcommand === 'change';
}

const subcommandToTitleMap = new Map<State['subcommand'], string>([
	['set', 'Set Upstream'],
	['unset', 'Unset Upstream'],
	['change', 'Change Upstream'],
]);

function getTitle(title: string, subcommand: State['subcommand'] | undefined) {
	return subcommand == null ? title : `${subcommandToTitleMap.get(subcommand)} ${title}`;
}

export interface UpstreamGitCommandArgs {
	readonly command: 'upstream';
	confirm?: boolean;
	state?: Partial<State>;
}

export class UpstreamGitCommand extends QuickCommand {
	private subcommand: State['subcommand'] | undefined;

	constructor(container: Container, args?: UpstreamGitCommandArgs) {
		super(container, 'upstream', 'upstream', 'Upstream', {
			description: 'set, change, or unset upstream tracking for branches',
		});

		let counter = 0;
		if (args?.state?.repo != null) {
			counter++;
		}

		if (args?.state?.branch != null) {
			counter++;
		}

		this.initialState = {
			counter: counter,
			confirm: args?.confirm,
			...args?.state,
		};
	}

	override get canConfirm(): boolean {
		return this.subcommand != null;
	}

	override get canSkipConfirm(): boolean {
		return false;
	}

	override get skipConfirmKey(): string {
		return `${this.key}${this.subcommand == null ? '' : `-${this.subcommand}`}:${this.pickedVia}`;
	}

	protected async *steps(state: PartialStepState<State>): StepGenerator {
		const context: Context = {
			repos: this.container.git.openRepositories,
			associatedView: this.container.views.branches,
			title: this.title,
		};

		if (state.subcommand == null) {
			this.subcommand = undefined;

			const result = yield* this.pickSubcommandStep(state);
			if (result === StepResultBreak) return result;

			state.subcommand = result;
		}

		this.subcommand = state.subcommand;

		if (state.repo == null || typeof state.repo === 'string') {
			const result = yield* pickRepositoryStep(state, context, 'Choose a repository');
			if (result === StepResultBreak) return result;

			state.repo = result;
		}

		if (state.branch == null) {
			// Ensure state has Repository object for pickBranchStep
			const stateWithRepo =
				typeof state.repo === 'string'
					? { ...state, repo: this.container.git.getRepository(state.repo)! }
					: state;

			const result = yield* pickBranchStep(stateWithRepo, context, {
				placeholder: 'Choose a branch to manage upstream tracking',
				picked: state.branch?.name,
			});
			if (result === StepResultBreak) return result;

			state.branch = result;
		}

		if (isSetState(state) || isChangeState(state)) {
			if (state.upstream == null) {
				const result = yield* this.pickUpstreamStep(state, context);
				if (result === StepResultBreak) return result;

				state.upstream = result;
			}
		}

		if (this.confirm(state.confirm)) {
			const result = yield* this.confirmStep(state as State, context);
			if (result === StepResultBreak) return result;
		}

		endSteps(state);
		void this.execute(state as State);
	}

	private *pickSubcommandStep(state: PartialStepState<State>): StepResultGenerator<State['subcommand']> {
		const step = createPickStep<QuickPickItemOfT<State['subcommand']>>({
			title: this.title,
			placeholder: 'Choose an upstream operation',
			items: [
				{
					label: 'Set Upstream',
					description: 'Set upstream tracking for a branch',
					item: 'set',
				},
				{
					label: 'Change Upstream',
					description: 'Change the upstream tracking for a branch',
					item: 'change',
				},
				{
					label: 'Unset Upstream',
					description: 'Remove upstream tracking from a branch',
					item: 'unset',
				},
			],
		});
		const selection: StepSelection<typeof step> = yield step;
		return canPickStepContinue(step, state, selection) ? selection[0].item : StepResultBreak;
	}

	private async execute(state: State): Promise<void> {
		const repo = typeof state.repo === 'string' ? state.repo : state.repo.path;

		try {
			switch (state.subcommand) {
				case 'set':
				case 'change':
					await this.container.git
						.getRepositoryService(repo)
						.branches.setUpstreamBranch(repo, state.branch.name, state.upstream.name);
					break;
				case 'unset':
					await this.container.git
						.getRepositoryService(repo)
						.branches.unsetUpstreamBranch(repo, state.branch.name);
					break;
			}

			// Reset cache to reflect changes
			this.container.events.fire('git:cache:reset', { repoPath: repo });
		} catch (ex) {
			Logger.error(ex, 'UpstreamGitCommand.execute');
			if (ex instanceof BranchError) {
				const action = state.subcommand === 'unset' ? 'unset upstream for' : 'set upstream for';
				void window.showErrorMessage(`Unable to ${action} branch '${state.branch.name}': ${ex.message}`);
			} else {
				void showGenericErrorMessage('Unable to manage upstream tracking');
			}
		}
	}

	private async *pickUpstreamStep(
		state: Partial<SetState | ChangeState>,
		context: Context,
	): AsyncStepResultGenerator<GitBranchReference> {
		const repo = typeof state.repo === 'string' ? this.container.git.getRepository(state.repo) : state.repo!;
		if (repo == null) return StepResultBreak;

		const branches = await repo.git.branches.getBranches({
			filter: b => isRemoteBranch(b.name) && b.name !== state.branch?.name,
		});

		const step = createPickStep<QuickPickItemOfT<GitBranchReference>>({
			title: getTitle(context.title, state.subcommand),
			placeholder: 'Choose an upstream branch',
			matchOnDescription: true,
			items: branches.values.map(b => ({
				label: b.getNameWithoutRemote(),
				description: `${b.getRemoteName()}/${b.getNameWithoutRemote()}`,
				item: b,
			})),
		});

		const selection: StepSelection<typeof step> = yield step;
		return canPickStepContinue(step, state, selection) ? selection[0].item : StepResultBreak;
	}

	private *confirmStep(state: State, context: Context): AsyncStepResultGenerator<void> {
		let message: string;
		switch (state.subcommand) {
			case 'set':
				message = `Set upstream of '${state.branch.name}' to '${state.upstream.name}'?`;
				break;
			case 'change':
				message = `Change upstream of '${state.branch.name}' to '${state.upstream.name}'?`;
				break;
			case 'unset':
				message = `Remove upstream tracking from '${state.branch.name}'?`;
				break;
		}

		// Ensure repo is a Repository object for appendReposToTitle
		const repoState =
			typeof state.repo === 'string' ? { ...state, repo: this.container.git.getRepository(state.repo)! } : state;

		const step = createConfirmStep(
			appendReposToTitle(getTitle(context.title, state.subcommand), repoState, context),
			[
				{
					label: 'Yes',
					description: message,
				},
			],
		);

		const selection: StepSelection<typeof step> = yield step;
		return canPickStepContinue(step, state, selection) ? undefined : StepResultBreak;
	}
}
