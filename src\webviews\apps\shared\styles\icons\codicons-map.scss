// This file is generated by (vscode-gitlens)/scripts/export-codicons.js
// Do not edit this file directly

$icon-font-family: 'codicon';

$icon-map: (
	'add': '\ea60',
	'plus': '\ea60',
	'gist-new': '\ea60',
	'repo-create': '\ea60',
	'lightbulb': '\ea61',
	'light-bulb': '\ea61',
	'repo': '\ea62',
	'repo-delete': '\ea62',
	'gist-fork': '\ea63',
	'repo-forked': '\ea63',
	'git-pull-request': '\ea64',
	'git-pull-request-abandoned': '\ea64',
	'record-keys': '\ea65',
	'keyboard': '\ea65',
	'tag': '\ea66',
	'git-pull-request-label': '\ea66',
	'tag-add': '\ea66',
	'tag-remove': '\ea66',
	'person': '\ea67',
	'person-follow': '\ea67',
	'person-outline': '\ea67',
	'person-filled': '\ea67',
	'git-branch': '\ea68',
	'git-branch-create': '\ea68',
	'git-branch-delete': '\ea68',
	'source-control': '\ea68',
	'mirror': '\ea69',
	'mirror-public': '\ea69',
	'star': '\ea6a',
	'star-add': '\ea6a',
	'star-delete': '\ea6a',
	'star-empty': '\ea6a',
	'comment': '\ea6b',
	'comment-add': '\ea6b',
	'alert': '\ea6c',
	'warning': '\ea6c',
	'search': '\ea6d',
	'search-save': '\ea6d',
	'log-out': '\ea6e',
	'sign-out': '\ea6e',
	'log-in': '\ea6f',
	'sign-in': '\ea6f',
	'eye': '\ea70',
	'eye-unwatch': '\ea70',
	'eye-watch': '\ea70',
	'circle-filled': '\ea71',
	'primitive-dot': '\ea71',
	'close-dirty': '\ea71',
	'debug-breakpoint': '\ea71',
	'debug-breakpoint-disabled': '\ea71',
	'debug-hint': '\ea71',
	'terminal-decoration-success': '\ea71',
	'primitive-square': '\ea72',
	'edit': '\ea73',
	'pencil': '\ea73',
	'info': '\ea74',
	'issue-opened': '\ea74',
	'gist-private': '\ea75',
	'git-fork-private': '\ea75',
	'lock': '\ea75',
	'mirror-private': '\ea75',
	'close': '\ea76',
	'remove-close': '\ea76',
	'x': '\ea76',
	'repo-sync': '\ea77',
	'sync': '\ea77',
	'clone': '\ea78',
	'desktop-download': '\ea78',
	'beaker': '\ea79',
	'microscope': '\ea79',
	'vm': '\ea7a',
	'device-desktop': '\ea7a',
	'file': '\ea7b',
	'file-text': '\ea7b',
	'more': '\ea7c',
	'ellipsis': '\ea7c',
	'kebab-horizontal': '\ea7c',
	'mail-reply': '\ea7d',
	'reply': '\ea7d',
	'organization': '\ea7e',
	'organization-filled': '\ea7e',
	'organization-outline': '\ea7e',
	'new-file': '\ea7f',
	'file-add': '\ea7f',
	'new-folder': '\ea80',
	'file-directory-create': '\ea80',
	'trash': '\ea81',
	'trashcan': '\ea81',
	'history': '\ea82',
	'clock': '\ea82',
	'folder': '\ea83',
	'file-directory': '\ea83',
	'symbol-folder': '\ea83',
	'logo-github': '\ea84',
	'mark-github': '\ea84',
	'github': '\ea84',
	'terminal': '\ea85',
	'console': '\ea85',
	'repl': '\ea85',
	'zap': '\ea86',
	'symbol-event': '\ea86',
	'error': '\ea87',
	'stop': '\ea87',
	'variable': '\ea88',
	'symbol-variable': '\ea88',
	'array': '\ea8a',
	'symbol-array': '\ea8a',
	'symbol-module': '\ea8b',
	'symbol-package': '\ea8b',
	'symbol-namespace': '\ea8b',
	'symbol-object': '\ea8b',
	'symbol-method': '\ea8c',
	'symbol-function': '\ea8c',
	'symbol-constructor': '\ea8c',
	'symbol-boolean': '\ea8f',
	'symbol-null': '\ea8f',
	'symbol-numeric': '\ea90',
	'symbol-number': '\ea90',
	'symbol-structure': '\ea91',
	'symbol-struct': '\ea91',
	'symbol-parameter': '\ea92',
	'symbol-type-parameter': '\ea92',
	'symbol-key': '\ea93',
	'symbol-text': '\ea93',
	'symbol-reference': '\ea94',
	'go-to-file': '\ea94',
	'symbol-enum': '\ea95',
	'symbol-value': '\ea95',
	'symbol-ruler': '\ea96',
	'symbol-unit': '\ea96',
	'activate-breakpoints': '\ea97',
	'archive': '\ea98',
	'arrow-both': '\ea99',
	'arrow-down': '\ea9a',
	'arrow-left': '\ea9b',
	'arrow-right': '\ea9c',
	'arrow-small-down': '\ea9d',
	'arrow-small-left': '\ea9e',
	'arrow-small-right': '\ea9f',
	'arrow-small-up': '\eaa0',
	'arrow-up': '\eaa1',
	'bell': '\eaa2',
	'bold': '\eaa3',
	'book': '\eaa4',
	'bookmark': '\eaa5',
	'debug-breakpoint-conditional-unverified': '\eaa6',
	'debug-breakpoint-conditional': '\eaa7',
	'debug-breakpoint-conditional-disabled': '\eaa7',
	'debug-breakpoint-data-unverified': '\eaa8',
	'debug-breakpoint-data': '\eaa9',
	'debug-breakpoint-data-disabled': '\eaa9',
	'debug-breakpoint-log-unverified': '\eaaa',
	'debug-breakpoint-log': '\eaab',
	'debug-breakpoint-log-disabled': '\eaab',
	'briefcase': '\eaac',
	'broadcast': '\eaad',
	'browser': '\eaae',
	'bug': '\eaaf',
	'calendar': '\eab0',
	'case-sensitive': '\eab1',
	'check': '\eab2',
	'checklist': '\eab3',
	'chevron-down': '\eab4',
	'chevron-left': '\eab5',
	'chevron-right': '\eab6',
	'chevron-up': '\eab7',
	'chrome-close': '\eab8',
	'chrome-maximize': '\eab9',
	'chrome-minimize': '\eaba',
	'chrome-restore': '\eabb',
	'circle-outline': '\eabc',
	'circle': '\eabc',
	'debug-breakpoint-unverified': '\eabc',
	'terminal-decoration-incomplete': '\eabc',
	'circle-slash': '\eabd',
	'circuit-board': '\eabe',
	'clear-all': '\eabf',
	'clippy': '\eac0',
	'close-all': '\eac1',
	'cloud-download': '\eac2',
	'cloud-upload': '\eac3',
	'code': '\eac4',
	'collapse-all': '\eac5',
	'color-mode': '\eac6',
	'comment-discussion': '\eac7',
	'credit-card': '\eac9',
	'dash': '\eacc',
	'dashboard': '\eacd',
	'database': '\eace',
	'debug-continue': '\eacf',
	'debug-disconnect': '\ead0',
	'debug-pause': '\ead1',
	'debug-restart': '\ead2',
	'debug-start': '\ead3',
	'debug-step-into': '\ead4',
	'debug-step-out': '\ead5',
	'debug-step-over': '\ead6',
	'debug-stop': '\ead7',
	'debug': '\ead8',
	'device-camera-video': '\ead9',
	'device-camera': '\eada',
	'device-mobile': '\eadb',
	'diff-added': '\eadc',
	'diff-ignored': '\eadd',
	'diff-modified': '\eade',
	'diff-removed': '\eadf',
	'diff-renamed': '\eae0',
	'diff': '\eae1',
	'diff-sidebyside': '\eae1',
	'discard': '\eae2',
	'editor-layout': '\eae3',
	'empty-window': '\eae4',
	'exclude': '\eae5',
	'extensions': '\eae6',
	'eye-closed': '\eae7',
	'file-binary': '\eae8',
	'file-code': '\eae9',
	'file-media': '\eaea',
	'file-pdf': '\eaeb',
	'file-submodule': '\eaec',
	'file-symlink-directory': '\eaed',
	'file-symlink-file': '\eaee',
	'file-zip': '\eaef',
	'files': '\eaf0',
	'filter': '\eaf1',
	'flame': '\eaf2',
	'fold-down': '\eaf3',
	'fold-up': '\eaf4',
	'fold': '\eaf5',
	'folder-active': '\eaf6',
	'folder-opened': '\eaf7',
	'gear': '\eaf8',
	'gift': '\eaf9',
	'gist-secret': '\eafa',
	'gist': '\eafb',
	'git-commit': '\eafc',
	'git-compare': '\eafd',
	'compare-changes': '\eafd',
	'git-merge': '\eafe',
	'github-action': '\eaff',
	'github-alt': '\eb00',
	'globe': '\eb01',
	'grabber': '\eb02',
	'graph': '\eb03',
	'gripper': '\eb04',
	'heart': '\eb05',
	'home': '\eb06',
	'horizontal-rule': '\eb07',
	'hubot': '\eb08',
	'inbox': '\eb09',
	'issue-reopened': '\eb0b',
	'issues': '\eb0c',
	'italic': '\eb0d',
	'jersey': '\eb0e',
	'json': '\eb0f',
	'kebab-vertical': '\eb10',
	'key': '\eb11',
	'law': '\eb12',
	'lightbulb-autofix': '\eb13',
	'link-external': '\eb14',
	'link': '\eb15',
	'list-ordered': '\eb16',
	'list-unordered': '\eb17',
	'live-share': '\eb18',
	'loading': '\eb19',
	'location': '\eb1a',
	'mail-read': '\eb1b',
	'mail': '\eb1c',
	'markdown': '\eb1d',
	'megaphone': '\eb1e',
	'mention': '\eb1f',
	'milestone': '\eb20',
	'git-pull-request-milestone': '\eb20',
	'mortar-board': '\eb21',
	'move': '\eb22',
	'multiple-windows': '\eb23',
	'mute': '\eb24',
	'no-newline': '\eb25',
	'note': '\eb26',
	'octoface': '\eb27',
	'open-preview': '\eb28',
	'package': '\eb29',
	'paintcan': '\eb2a',
	'pin': '\eb2b',
	'play': '\eb2c',
	'run': '\eb2c',
	'plug': '\eb2d',
	'preserve-case': '\eb2e',
	'preview': '\eb2f',
	'project': '\eb30',
	'pulse': '\eb31',
	'question': '\eb32',
	'quote': '\eb33',
	'radio-tower': '\eb34',
	'reactions': '\eb35',
	'references': '\eb36',
	'refresh': '\eb37',
	'regex': '\eb38',
	'remote-explorer': '\eb39',
	'remote': '\eb3a',
	'remove': '\eb3b',
	'replace-all': '\eb3c',
	'replace': '\eb3d',
	'repo-clone': '\eb3e',
	'repo-force-push': '\eb3f',
	'repo-pull': '\eb40',
	'repo-push': '\eb41',
	'report': '\eb42',
	'request-changes': '\eb43',
	'rocket': '\eb44',
	'root-folder-opened': '\eb45',
	'root-folder': '\eb46',
	'rss': '\eb47',
	'ruby': '\eb48',
	'save-all': '\eb49',
	'save-as': '\eb4a',
	'save': '\eb4b',
	'screen-full': '\eb4c',
	'screen-normal': '\eb4d',
	'search-stop': '\eb4e',
	'server': '\eb50',
	'settings-gear': '\eb51',
	'settings': '\eb52',
	'shield': '\eb53',
	'smiley': '\eb54',
	'sort-precedence': '\eb55',
	'split-horizontal': '\eb56',
	'split-vertical': '\eb57',
	'squirrel': '\eb58',
	'star-full': '\eb59',
	'star-half': '\eb5a',
	'symbol-class': '\eb5b',
	'symbol-color': '\eb5c',
	'symbol-constant': '\eb5d',
	'symbol-enum-member': '\eb5e',
	'symbol-field': '\eb5f',
	'symbol-file': '\eb60',
	'symbol-interface': '\eb61',
	'symbol-keyword': '\eb62',
	'symbol-misc': '\eb63',
	'symbol-operator': '\eb64',
	'symbol-property': '\eb65',
	'wrench': '\eb65',
	'wrench-subaction': '\eb65',
	'symbol-snippet': '\eb66',
	'tasklist': '\eb67',
	'telescope': '\eb68',
	'text-size': '\eb69',
	'three-bars': '\eb6a',
	'thumbsdown': '\eb6b',
	'thumbsup': '\eb6c',
	'tools': '\eb6d',
	'triangle-down': '\eb6e',
	'triangle-left': '\eb6f',
	'triangle-right': '\eb70',
	'triangle-up': '\eb71',
	'twitter': '\eb72',
	'unfold': '\eb73',
	'unlock': '\eb74',
	'unmute': '\eb75',
	'unverified': '\eb76',
	'verified': '\eb77',
	'versions': '\eb78',
	'vm-active': '\eb79',
	'vm-outline': '\eb7a',
	'vm-running': '\eb7b',
	'watch': '\eb7c',
	'whitespace': '\eb7d',
	'whole-word': '\eb7e',
	'window': '\eb7f',
	'word-wrap': '\eb80',
	'zoom-in': '\eb81',
	'zoom-out': '\eb82',
	'list-filter': '\eb83',
	'list-flat': '\eb84',
	'list-selection': '\eb85',
	'selection': '\eb85',
	'list-tree': '\eb86',
	'debug-breakpoint-function-unverified': '\eb87',
	'debug-breakpoint-function': '\eb88',
	'debug-breakpoint-function-disabled': '\eb88',
	'debug-stackframe-active': '\eb89',
	'circle-small-filled': '\eb8a',
	'debug-stackframe-dot': '\eb8a',
	'terminal-decoration-mark': '\eb8a',
	'debug-stackframe': '\eb8b',
	'debug-stackframe-focused': '\eb8b',
	'debug-breakpoint-unsupported': '\eb8c',
	'symbol-string': '\eb8d',
	'debug-reverse-continue': '\eb8e',
	'debug-step-back': '\eb8f',
	'debug-restart-frame': '\eb90',
	'debug-alt': '\eb91',
	'call-incoming': '\eb92',
	'call-outgoing': '\eb93',
	'menu': '\eb94',
	'expand-all': '\eb95',
	'feedback': '\eb96',
	'git-pull-request-reviewer': '\eb96',
	'group-by-ref-type': '\eb97',
	'ungroup-by-ref-type': '\eb98',
	'account': '\eb99',
	'git-pull-request-assignee': '\eb99',
	'bell-dot': '\eb9a',
	'debug-console': '\eb9b',
	'library': '\eb9c',
	'output': '\eb9d',
	'run-all': '\eb9e',
	'sync-ignored': '\eb9f',
	'pinned': '\eba0',
	'github-inverted': '\eba1',
	'server-process': '\eba2',
	'server-environment': '\eba3',
	'pass': '\eba4',
	'issue-closed': '\eba4',
	'stop-circle': '\eba5',
	'play-circle': '\eba6',
	'record': '\eba7',
	'debug-alt-small': '\eba8',
	'vm-connect': '\eba9',
	'cloud': '\ebaa',
	'merge': '\ebab',
	'export': '\ebac',
	'graph-left': '\ebad',
	'magnet': '\ebae',
	'notebook': '\ebaf',
	'redo': '\ebb0',
	'check-all': '\ebb1',
	'pinned-dirty': '\ebb2',
	'pass-filled': '\ebb3',
	'circle-large-filled': '\ebb4',
	'circle-large': '\ebb5',
	'circle-large-outline': '\ebb5',
	'combine': '\ebb6',
	'gather': '\ebb6',
	'table': '\ebb7',
	'variable-group': '\ebb8',
	'type-hierarchy': '\ebb9',
	'type-hierarchy-sub': '\ebba',
	'type-hierarchy-super': '\ebbb',
	'git-pull-request-create': '\ebbc',
	'run-above': '\ebbd',
	'run-below': '\ebbe',
	'notebook-template': '\ebbf',
	'debug-rerun': '\ebc0',
	'workspace-trusted': '\ebc1',
	'workspace-untrusted': '\ebc2',
	'workspace-unknown': '\ebc3',
	'terminal-cmd': '\ebc4',
	'terminal-debian': '\ebc5',
	'terminal-linux': '\ebc6',
	'terminal-powershell': '\ebc7',
	'terminal-tmux': '\ebc8',
	'terminal-ubuntu': '\ebc9',
	'terminal-bash': '\ebca',
	'arrow-swap': '\ebcb',
	'copy': '\ebcc',
	'person-add': '\ebcd',
	'filter-filled': '\ebce',
	'wand': '\ebcf',
	'debug-line-by-line': '\ebd0',
	'inspect': '\ebd1',
	'layers': '\ebd2',
	'layers-dot': '\ebd3',
	'layers-active': '\ebd4',
	'compass': '\ebd5',
	'compass-dot': '\ebd6',
	'compass-active': '\ebd7',
	'azure': '\ebd8',
	'issue-draft': '\ebd9',
	'git-pull-request-closed': '\ebda',
	'git-pull-request-draft': '\ebdb',
	'debug-all': '\ebdc',
	'debug-coverage': '\ebdd',
	'run-errors': '\ebde',
	'folder-library': '\ebdf',
	'debug-continue-small': '\ebe0',
	'beaker-stop': '\ebe1',
	'graph-line': '\ebe2',
	'graph-scatter': '\ebe3',
	'pie-chart': '\ebe4',
	'bracket': '\eb0f',
	'bracket-dot': '\ebe5',
	'bracket-error': '\ebe6',
	'lock-small': '\ebe7',
	'azure-devops': '\ebe8',
	'verified-filled': '\ebe9',
	'newline': '\ebea',
	'layout': '\ebeb',
	'layout-activitybar-left': '\ebec',
	'layout-activitybar-right': '\ebed',
	'layout-panel-left': '\ebee',
	'layout-panel-center': '\ebef',
	'layout-panel-justify': '\ebf0',
	'layout-panel-right': '\ebf1',
	'layout-panel': '\ebf2',
	'layout-sidebar-left': '\ebf3',
	'layout-sidebar-right': '\ebf4',
	'layout-statusbar': '\ebf5',
	'layout-menubar': '\ebf6',
	'layout-centered': '\ebf7',
	'target': '\ebf8',
	'indent': '\ebf9',
	'record-small': '\ebfa',
	'error-small': '\ebfb',
	'terminal-decoration-error': '\ebfb',
	'arrow-circle-down': '\ebfc',
	'arrow-circle-left': '\ebfd',
	'arrow-circle-right': '\ebfe',
	'arrow-circle-up': '\ebff',
	'layout-sidebar-right-off': '\ec00',
	'layout-panel-off': '\ec01',
	'layout-sidebar-left-off': '\ec02',
	'blank': '\ec03',
	'heart-filled': '\ec04',
	'map': '\ec05',
	'map-horizontal': '\ec05',
	'fold-horizontal': '\ec05',
	'map-filled': '\ec06',
	'map-horizontal-filled': '\ec06',
	'fold-horizontal-filled': '\ec06',
	'circle-small': '\ec07',
	'bell-slash': '\ec08',
	'bell-slash-dot': '\ec09',
	'comment-unresolved': '\ec0a',
	'git-pull-request-go-to-changes': '\ec0b',
	'git-pull-request-new-changes': '\ec0c',
	'search-fuzzy': '\ec0d',
	'comment-draft': '\ec0e',
	'send': '\ec0f',
	'sparkle': '\ec10',
	'insert': '\ec11',
	'mic': '\ec12',
	'thumbsdown-filled': '\ec13',
	'thumbsup-filled': '\ec14',
	'coffee': '\ec15',
	'snake': '\ec16',
	'game': '\ec17',
	'vr': '\ec18',
	'chip': '\ec19',
	'piano': '\ec1a',
	'music': '\ec1b',
	'mic-filled': '\ec1c',
	'repo-fetch': '\ec1d',
	'copilot': '\ec1e',
	'lightbulb-sparkle': '\ec1f',
	'robot': '\ec20',
	'sparkle-filled': '\ec21',
	'diff-single': '\ec22',
	'diff-multiple': '\ec23',
	'surround-with': '\ec24',
	'share': '\ec25',
	'git-stash': '\ec26',
	'git-stash-apply': '\ec27',
	'git-stash-pop': '\ec28',
	'vscode': '\ec29',
	'vscode-insiders': '\ec2a',
	'code-oss': '\ec2b',
	'run-coverage': '\ec2c',
	'run-all-coverage': '\ec2d',
	'coverage': '\ec2e',
	'github-project': '\ec2f',
	'map-vertical': '\ec30',
	'fold-vertical': '\ec30',
	'map-vertical-filled': '\ec31',
	'fold-vertical-filled': '\ec31',
	'go-to-search': '\ec32',
	'percentage': '\ec33',
	'sort-percentage': '\ec33'
);
