; Documentation of this file format -> http://supervisord.org/configuration.html

; Priority 0 - xvfb & fluxbox, 5 - x11vnc, 10 - noVNC, 15 - selenium-node

[program:xvfb]
priority=0
command=/opt/bin/start-xvfb.sh
autostart=true
autorestart=true

;Logs
redirect_stderr=false
stdout_logfile=/var/log/supervisor/xvfb-stdout.log
stderr_logfile=/var/log/supervisor/xvfb-stderr.log
stdout_logfile_maxbytes=50MB
stderr_logfile_maxbytes=50MB
stdout_logfile_backups=5
stderr_logfile_backups=5
stdout_capture_maxbytes=50MB
stderr_capture_maxbytes=50MB

[program:vnc]
priority=5
command=/opt/bin/start-vnc.sh
autostart=true
autorestart=true

;Logs
redirect_stderr=false
stdout_logfile=/var/log/supervisor/vnc-stdout.log
stderr_logfile=/var/log/supervisor/vnc-stderr.log
stdout_logfile_maxbytes=50MB
stderr_logfile_maxbytes=50MB
stdout_logfile_backups=5
stderr_logfile_backups=5
stdout_capture_maxbytes=50MB
stderr_capture_maxbytes=50MB

[program:novnc]
priority=10
command=/opt/bin/start-novnc.sh
autostart=true
autorestart=true

;Logs
redirect_stderr=false
stdout_logfile=/var/log/supervisor/novnc-stdout.log
stderr_logfile=/var/log/supervisor/novnc-stderr.log
stdout_logfile_maxbytes=50MB
stderr_logfile_maxbytes=50MB
stdout_logfile_backups=5
stderr_logfile_backups=5
stdout_capture_maxbytes=50MB
stderr_capture_maxbytes=50MB

[program:selenium-node]
priority=15
command=bash -c "cd /opt/vscode-gitlens && ./tests/docker/run-unit-tests.sh"
stopasgroup = true
autostart=true
autorestart=false
startsecs=0
startretries=0

;Logs (all Hub activity redirected to stdout so it can be seen through "docker logs"
redirect_stderr=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
